{"name": "nuxt-app", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"@nuxt/eslint": "1.9.0", "@nuxt/image": "1.11.0", "@nuxt/ui": "3.3.2", "@pinia/nuxt": "^0.11.2", "eslint": "^9.0.0", "nuxt": "^4.0.3", "pinia": "^3.0.3", "typescript": "^5.6.3", "vue": "^3.5.20", "vue-router": "^4.5.1", "zod": "^4.1.5"}, "devDependencies": {"@iconify-json/heroicons": "^1.2.2"}}